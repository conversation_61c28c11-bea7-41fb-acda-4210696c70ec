import os
import sys
import time
import base64
import io
from pathlib import Path
from typing import List

from fastapi import FastAP<PERSON>, Body, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

import pandas as pd

# --------------------------
# FastAPI Setup
# --------------------------
app = FastAPI(title="Tele Taleem PowerBill API", version="3.4")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --------------------------
# Reference numbers file
# --------------------------
ref_file_path = Path("/home/<USER>/Desktop/refrence numbers/ref_numbers.txt")
ref_file_path.parent.mkdir(parents=True, exist_ok=True)

if not ref_file_path.exists():
    ref_file_path.touch()
    print(f"⚠️ Created empty reference numbers file at {ref_file_path}")
else:
    print(f"✅ Using reference numbers file: {ref_file_path}")

# --------------------------
# PDF Download Directory (Desktop)
# --------------------------
desktop_path = Path.home() / "Desktop"
download_dir = desktop_path / "IESCO_Bills"
download_dir.mkdir(parents=True, exist_ok=True)
print(f"✅ Bills will be saved in: {download_dir}")

# Serve PDFs as static files
app.mount("/static", StaticFiles(directory=download_dir), name="static")

# --------------------------
# Selenium Chrome Driver
# --------------------------
def get_chrome_driver(headless=False):
    options = Options()
    if headless:
        options.add_argument("--headless=new")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-infobars")
    options.add_argument("--window-size=1920,1080")
    service = Service(ChromeDriverManager().install())
    return webdriver.Chrome(service=service, options=options)

# --------------------------
# Save PDF
# --------------------------
def save_pdf(driver, save_path):
    pdf = driver.execute_cdp_cmd("Page.printToPDF", {"printBackground": True, "format": "A4"})
    with open(save_path, "wb") as f:
        f.write(base64.b64decode(pdf["data"]))
    print(f"✅ Saved: {save_path}")

# --------------------------
# Close extra tabs
# --------------------------
def close_extra_tabs(driver, main_handle):
    for handle in driver.window_handles:
        if handle != main_handle:
            try:
                driver.switch_to.window(handle)
                driver.close()
            except:
                pass
    driver.switch_to.window(main_handle)

# --------------------------
# Download single bill
# --------------------------
def download_single_bill(driver, ref_number: str):
    driver.get("https://iescobill.pk/")
    main_window = driver.current_window_handle

    # Enter reference number
    ref_input = WebDriverWait(driver, 15).until(
        EC.presence_of_element_located((By.ID, "reference"))
    )
    ref_input.clear()
    ref_input.send_keys(ref_number)

    driver.find_element(By.ID, "checkBill").click()
    time.sleep(2)
    close_extra_tabs(driver, main_window)

    # Click the submit button to open bill
    open_btn = WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "form#billForm button[type='submit']"))
    )
    open_btn.click()

    WebDriverWait(driver, 15).until(lambda d: len(d.window_handles) > 1)
    driver.switch_to.window(driver.window_handles[-1])

    # Wait until bill content is loaded
    try:
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.bill-container"))
        )
    except:
        time.sleep(3)  # fallback wait

    # Save PDF
    save_path = download_dir / f"{ref_number}.pdf"
    save_pdf(driver, str(save_path))

# --------------------------
# API: Run all bills automatically
# --------------------------
@app.get("/api/run-file")
async def run_file_bills():
    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]

    if not ref_numbers:
        return JSONResponse(status_code=400, content={"error": "No reference numbers found"})

    driver = get_chrome_driver(headless=False)
    results: List[dict] = []
    try:
        for ref in ref_numbers:
            try:
                download_single_bill(driver, ref)
                results.append({"refNumber": ref, "status": "✅ Completed"})
            except Exception as e:
                results.append({"refNumber": ref, "status": f"❌ Failed: {e}"})
        return {"results": results}
    finally:
        driver.quit()

# --------------------------
# API: Download a single bill by reference number
# --------------------------
@app.post("/api/run-single")
async def run_single_bill(payload: dict = Body(...)):
    ref_number = payload.get("refNumber")
    if not ref_number:
        return JSONResponse(status_code=400, content={"error": "No reference number provided"})

    driver = get_chrome_driver(headless=False)
    try:
        download_single_bill(driver, ref_number)
        return {"file_path": f"/static/{ref_number}.pdf", "message": "✅ Completed"}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})
    finally:
        driver.quit()

# --------------------------
# ✅ API: Upload Excel file (updated)
# --------------------------
@app.post("/api/upload-excel")
async def upload_excel(file: UploadFile = File(...)):
    fname = file.filename.lower()
    if not fname.endswith((".xlsx", ".xls")):
        return JSONResponse(status_code=400, content={"error": "Invalid file format. Please upload an Excel file."})

    try:
        contents = await file.read()
        buffer = io.BytesIO(contents)

        if fname.endswith(".xlsx"):
            df = pd.read_excel(buffer, engine="openpyxl")
        else:
            df = pd.read_excel(buffer, engine="xlrd")

        references = (
            df.iloc[:, 0]
            .dropna()
            .astype(str)
            .str.replace(r"\s+", "", regex=True)
            .tolist()
        )

        with open(ref_file_path, "w") as f:
            for ref in references:
                f.write(ref + "\n")

        return {"message": "✅ File uploaded successfully", "total_refs": len(references)}

    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

# --------------------------
# Health check
# --------------------------
@app.get("/api/health")
async def health_check():
    return {"status": "OK", "message": "API is running 🚀"}

# --------------------------
# CLI Mode
# --------------------------
def run_all_bills_from_file():
    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]

    if not ref_numbers:
        print("❌ No reference numbers found")
        return

    driver = get_chrome_driver(headless=False)
    try:
        for ref in ref_numbers:
            try:
                print(f"⚡ Downloading bill: {ref}")
                download_single_bill(driver, ref)
            except Exception as e:
                print(f"❌ Failed to download {ref}: {e}")
        print("🎉 All downloads finished.")
    finally:
        driver.quit()

# --------------------------
# Main
# --------------------------
if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--server":
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
    else:
        run_all_bills_from_file()
