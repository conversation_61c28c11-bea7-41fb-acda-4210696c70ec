import React, { useState } from "react";
import "./App.css";

export default function DownloadBill({ showList }) {
  const [refNumber, setRefNumber] = useState("");
  const [status, setStatus] = useState("");
  const [statusList, setStatusList] = useState([]);
  const [loadingFile, setLoadingFile] = useState(false);
  const [uploadStatus, setUploadStatus] = useState("");

  // Download single bill
  const downloadBill = async () => {
    if (!refNumber.trim()) {
      setStatus("❌ Please enter a reference number!");
      return;
    }
    setStatus("⏳ Downloading...");
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-single", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refNumber }),
      });
      const data = await res.json();
      if (res.ok) {
        setStatus(`✅ ${data.message || "Bill downloaded successfully"}`);
      } else {
        setStatus(`❌ ${data.detail || "Failed to download bill"}`);
      }
    } catch (err) {
      setStatus("❌ Error connecting to server");
    }
  };

  // Download bills from uploaded file
  const downloadAllBills = async () => {
    setLoadingFile(true);
    setStatusList([]);
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-file");
      const data = await res.json();
      if (res.ok) {
        setStatusList(data.results || []);
      } else {
        setStatusList([{ refNumber: "-", status: `❌ ${data.error || "Failed to process file"}` }]);
      }
    } catch {
      setStatusList([{ refNumber: "-", status: "❌ Error connecting to server" }]);
    } finally {
      setLoadingFile(false);
    }
  };

  // Handle Excel file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    if (!/\.(xlsx|xls)$/i.test(file.name)) {
      setUploadStatus("❌ Only Excel files (.xlsx, .xls) are allowed");
      return;
    }

    setUploadStatus("⏳ Uploading...");
    const formData = new FormData();
    formData.append("file", file);

    try {
      const res = await fetch("http://127.0.0.1:8000/api/upload-excel", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      if (res.ok) {
        setUploadStatus(`✅ ${data.message || "File uploaded successfully"}`);
      } else {
        setUploadStatus(`❌ ${data.detail || "Upload failed"}`);
      }
    } catch {
      setUploadStatus("❌ Error connecting to server");
    }
  };

  return (
    <div className="card p-4">
      {/* Single Bill Section */}
      <h3 style={{ color: "#343a40" }} className="text-center mb-3">
        Download Single Bill
      </h3>
      <div className="input-group mb-3">
        <input
          type="text"
          className="form-control"
          placeholder="Enter Reference Number"
          value={refNumber}
          onChange={(e) => setRefNumber(e.target.value)}
        />
        <button
          style={{ backgroundColor: "#714B67", color: "white" }}
          className="btn"
          onClick={downloadBill}
        >
          Download
        </button>
      </div>
      {status && <p className="fw-bold">{status}</p>}

      <hr />

      {/* Upload Excel Section */}
      <h3 style={{ color: "#343a40" }} className="text-center mb-3">
        Upload Excel File
      </h3>
      <div className="mb-3">
        <input
          type="file"
          className="form-control"
          accept=".xlsx, .xls"
          onChange={handleFileUpload}
        />
      </div>
      {uploadStatus && <p className="fw-bold">{uploadStatus}</p>}

      <hr />

      {/* File Download Section */}
      <h3 style={{ color: "#343a40" }} className="text-center mb-3">
        Download Bills from File
      </h3>
      <div className="d-flex justify-content-center mb-3">
        <button
          style={{ backgroundColor: "#714B67", color: "white" }}
          className="btn"
          onClick={downloadAllBills}
          disabled={loadingFile}
        >
          {loadingFile ? "⏳ Downloading..." : "Download All Bills"}
        </button>
        <button
          style={{ backgroundColor: "#714B67", color: "white" }}
          className="btn ms-2"
          onClick={showList}
        >
          View List
        </button>
      </div>

      {/* Status Table */}
      {statusList.length > 0 && (
        <table className="table table-bordered table-striped mt-3">
          <thead className="table-dark">
            <tr>
              <th>Reference Number</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {statusList.map((item, idx) => (
              <tr key={idx}>
                <td>{item.refNumber}</td>
                <td>{item.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
